import React, { useState, useEffect } from "react";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { FaEye, FaTrash } from "react-icons/fa";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

// Badge
const Badge = ({ text, color }) => (
  <span className={`px-2 py-1 text-xs font-medium rounded-full ${color}`}>
    {text}
  </span>
);

// Modal
const Modal = ({ title, children, onClose }) => (
  <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50 p-4">
    <div className="bg-white rounded-lg shadow-lg max-w-md w-full p-6">
      <div className="flex justify-between items-center border-b pb-2 mb-4">
        <h2 className="text-lg font-semibold">{title}</h2>
        <button className="text-gray-500 hover:text-gray-700" onClick={onClose}>
          ✖
        </button>
      </div>
      {children}
    </div>
  </div>
);

const ManageClasses = () => {
  const [selectedClass, setSelectedClass] = useState(null);
  const [classType, setClassType] = useState("Homeroom");

  const [classes, setClasses] = useState([
    {
      id: 1,
      name: "10A1",
      type: "Homeroom",
      teacher: "Mr. John",
      students: 40,
      status: "Active",
    },
    {
      id: 2,
      name: "Physics 12B",
      type: "Subject",
      subject: "Physics",
      teacher: "Mrs. Smith",
      students: 35,
      status: "Active",
    },
  ]);

  const [modalType, setModalType] = useState(null); // add | edit | delete

  useEffect(() => {
    if (selectedClass) {
      setClassType(selectedClass.type || "Homeroom");
    } else {
      setClassType("Homeroom");
    }
  }, [selectedClass]);

  const openModal = (type, cls = null) => {
    setSelectedClass(cls);
    setModalType(type);
  };

  const closeModal = () => {
    setSelectedClass(null);
    setModalType(null);
  };

  const handleDelete = (id) => {
    setClasses(classes.filter((c) => c.id !== id));
    closeModal();
  };

  const chartData = {
    labels: classes.map((c) => c.name),
    datasets: [
      {
        label: "Students",
        data: classes.map((c) => c.students),
        backgroundColor: "#3b82f6",
      },
    ],
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between md:items-center gap-4">
        <h1 className="text-2xl font-bold">Class Management</h1>
        <button
          onClick={() => openModal("add")}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
        >
          + Add New Class
        </button>
      </div>

      {/* Chart */}
      <div className="flex justify-center">
        <div className="bg-white p-4 rounded-lg shadow w-1/3">
          <h2 className="text-lg font-semibold mb-4">
            Student Count per Class
          </h2>
          <Bar data={chartData} />
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto bg-white rounded-lg shadow">
        <table className="w-full text-sm text-left">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-2">Class Name</th>
              <th className="px-4 py-2">Type</th>
              <th className="px-4 py-2">Subject</th>
              <th className="px-4 py-2">Teacher</th>
              <th className="px-4 py-2">Students</th>
              <th className="px-4 py-2">Status</th>
              <th className="px-4 py-2">Actions</th>
            </tr>
          </thead>

          <tbody>
            {classes.map((c) => (
              <tr key={c.id} className="border-t">
                <td className="px-4 py-2 font-medium">{c.name}</td>
                <td className="px-4 py-2">{c.type}</td>
                <td className="px-4 py-2">
                  {c.type === "Subject" ? c.subject : "-"}
                </td>
                <td className="px-4 py-2">{c.teacher}</td>
                <td className="px-4 py-2">{c.students}</td>
                <td className="px-4 py-2">
                  <Badge
                    text={c.status}
                    color={
                      c.status === "Active"
                        ? "bg-green-100 text-green-700"
                        : "bg-red-100 text-red-700"
                    }
                  />
                </td>
                <td className="px-4 py-2 flex gap-2">
                  <button
                    onClick={() => openModal("edit", c)}
                    className="text-blue-600 hover:underline"
                  >
                    <FaEye />
                  </button>
                  <button
                    onClick={() => openModal("delete", c)}
                    className="text-red-600 hover:underline"
                  >
                    <FaTrash />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal Add/Edit */}
      {(modalType === "add" || modalType === "edit") && (
        <Modal
          title={modalType === "add" ? "Add New Class" : "Edit Class"}
          onClose={closeModal}
        >
          <form className="space-y-4">
            <input
              type="text"
              placeholder="Class Name"
              defaultValue={selectedClass?.name || ""}
              className="w-full px-4 py-2 border rounded-lg"
            />

            <select
              value={classType}
              onChange={(e) => setClassType(e.target.value)}
              className="w-full px-4 py-2 border rounded-lg"
            >
              <option value="Homeroom">Homeroom</option>
              <option value="Subject">Subject</option>
            </select>

            {classType === "Subject" && (
              <input
                type="text"
                placeholder="Subject"
                defaultValue={selectedClass?.subject || ""}
                className="w-full px-4 py-2 border rounded-lg"
              />
            )}

            <input
              type="text"
              placeholder="Teacher"
              defaultValue={selectedClass?.teacher || ""}
              className="w-full px-4 py-2 border rounded-lg"
            />

            <input
              type="number"
              placeholder="Number of Students"
              defaultValue={selectedClass?.students || ""}
              className="w-full px-4 py-2 border rounded-lg"
            />

            <select
              defaultValue={selectedClass?.status || "Active"}
              className="w-full px-4 py-2 border rounded-lg"
            >
              <option>Active</option>
              <option>Inactive</option>
            </select>

            <div className="flex justify-end gap-2">
              <button
                type="button"
                onClick={closeModal}
                className="px-4 py-2 border rounded-lg"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg"
              >
                Save
              </button>
            </div>
          </form>
        </Modal>
      )}

      {/* Modal Delete */}
      {modalType === "delete" && selectedClass && (
        <Modal title="Confirm Delete" onClose={closeModal}>
          <p>
            Are you sure you want to delete{" "}
            <strong>{selectedClass.name}</strong>?
          </p>
          <div className="flex justify-end gap-2 mt-4">
            <button
              onClick={closeModal}
              className="px-4 py-2 border rounded-lg"
            >
              Cancel
            </button>
            <button
              onClick={() => handleDelete(selectedClass.id)}
              className="px-4 py-2 bg-red-600 text-white rounded-lg"
            >
              Delete
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ManageClasses;
